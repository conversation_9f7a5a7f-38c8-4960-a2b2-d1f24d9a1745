import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black flex flex-col items-center justify-center">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full">
        {/* Large Logo with ShapeBlur Effect Container */}
        <div className="relative flex items-center justify-center mb-20">
          {/* ShapeBlur Effect - Large rectangular container */}
          <div className="w-96 h-64 relative">
            <ShapeBlur
              variation={0}
              shapeSize={0.9}
              roundness={0.2}
              borderSize={0.05}
              circleSize={0.3}
              circleEdge={0.7}
            />
            {/* Large Logo positioned absolutely in center */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
              <img
                src="/logo.png"
                alt="Logo"
                className="w-48 h-48 object-contain"
                style={{
                  filter: 'drop-shadow(0 0 15px rgba(255,255,255,0.4)) brightness(1.1)',
                  backgroundColor: 'transparent'
                }}
              />
            </div>
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-6xl md:text-8xl font-thin text-white tracking-widest">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App

import Shape<PERSON>lur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black flex flex-col items-center justify-center">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center">
        {/* Logo with ShapeBlur Effect Container */}
        <div className="relative flex items-center justify-center mb-16">
          {/* ShapeBlur Effect - Fixed size container */}
          <div className="w-64 h-48 relative">
            <ShapeBlur
              variation={0}
              shapeSize={0.8}
              roundness={0.3}
              borderSize={0.08}
              circleSize={0.4}
              circleEdge={0.6}
            />
            {/* <PERSON><PERSON> positioned absolutely in center */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
              <img
                src="/logo.png"
                alt="Logo"
                className="w-20 h-20 object-contain"
                style={{
                  filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.6)) brightness(1.2)'
                }}
              />
            </div>
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-thin text-white tracking-widest">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App

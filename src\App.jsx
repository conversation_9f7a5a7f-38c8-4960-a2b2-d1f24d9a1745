import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="maintenance-container">
      {/* Hyperspeed Background */}
      <div className="background-layer">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* ShapeBlur Effect Layer */}
      <div className="shapeblur-layer">
        <ShapeBlur
          variation={0}
          shapeSize={1.2}
          roundness={0.4}
          borderSize={0.05}
          circleSize={0.3}
          circleEdge={0.5}
        />
      </div>

      {/* Content Layer */}
      <div className="content-layer">
        <div className="logo-container">
          <img src="/logo.png" alt="Logo" className="logo" />
        </div>
        <div className="maintenance-text">
          <h1>Under Maintenance</h1>
        </div>
      </div>
    </div>
  )
}

export default App

import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black flex flex-col items-center justify-center">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full">
        {/* Full Width Logo with ShapeBlur Effect Container */}
        <div className="relative w-full flex items-center justify-center mb-16">
          {/* ShapeBlur Effect - Full screen container */}
          <div className="w-screen h-[50vh] relative overflow-hidden">
            <ShapeBlur
              variation={0}
              shapeSize={1.2}
              roundness={0.02}
              borderSize={0.3}
              circleSize={0.8}
              circleEdge={0.15}
            />
            {/* Full Width Logo positioned absolutely in center */}
            <div className="absolute inset-0 flex items-center justify-center z-20 maintenance-logo">
              <img
                src="/logo.png"
                alt="Novix Studios Logo"
                className="w-[85vw] h-[25vh] object-contain maintenance-logo"
                style={{
                  filter: 'brightness(4) contrast(4) saturate(3) drop-shadow(0 0 80px rgba(255,255,255,1)) drop-shadow(0 0 40px rgba(255,255,255,0.8))',
                  mixBlendMode: 'screen',
                  background: 'none',
                  backgroundColor: 'transparent',
                  imageRendering: 'crisp-edges'
                }}
              />
            </div>
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-thin text-white tracking-widest">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App

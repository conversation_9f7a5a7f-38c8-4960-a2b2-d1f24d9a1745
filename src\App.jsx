import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black flex flex-col items-center justify-center">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full">
        {/* Massive Logo with ShapeBlur Effect Container */}
        <div className="relative flex items-center justify-center mb-16">
          {/* ShapeBlur Effect - Viewport-sized container */}
          <div className="w-[80vw] h-[60vh] relative">
            <ShapeBlur
              variation={0}
              shapeSize={0.95}
              roundness={0.15}
              borderSize={0.15}
              circleSize={0.5}
              circleEdge={0.4}
            />
            {/* Massive Logo positioned absolutely in center */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
              <img
                src="/logo.png"
                alt="Logo"
                className="w-80 h-80 object-contain mix-blend-screen"
                style={{
                  filter: 'drop-shadow(0 0 30px rgba(255,255,255,0.8)) brightness(1.5) contrast(1.3)',
                  backgroundColor: 'transparent',
                  mixBlendMode: 'screen'
                }}
              />
            </div>
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-thin text-white tracking-widest">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App

import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full">
        {/* Logo with ShapeBlur Effect */}
        <div className="relative w-96 h-96 mb-12">
          {/* ShapeBlur Effect */}
          <div className="absolute inset-0">
            <ShapeBlur
              variation={1}
              shapeSize={0.7}
              roundness={0.9}
              borderSize={0.2}
              circleSize={0.8}
              circleEdge={0.3}
            />
          </div>
          {/* Logo centered inside */}
          <div className="absolute inset-0 flex items-center justify-center">
            <img
              src="/logo.png"
              alt="Logo"
              className="w-32 h-32 object-contain filter drop-shadow-2xl brightness-110"
            />
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-light text-white tracking-wider">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App

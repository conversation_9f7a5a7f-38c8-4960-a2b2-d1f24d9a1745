import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="maintenance-container">
      {/* Hyperspeed Background */}
      <div className="background-layer">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer with integrated ShapeBlur */}
      <div className="content-layer">
        <div className="main-content">
          <div className="logo-blur-container">
            {/* ShapeBlur Effect around logo */}
            <div className="shapeblur-wrapper">
              <ShapeBlur
                variation={1}
                shapeSize={0.9}
                roundness={0.8}
                borderSize={0.15}
                circleSize={0.6}
                circleEdge={0.4}
              />
            </div>
            {/* Logo centered inside blur */}
            <div className="logo-container">
              <img src="/logo.png" alt="Logo" className="logo" />
            </div>
          </div>
          <div className="maintenance-text">
            <h1>Under Maintenance</h1>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App

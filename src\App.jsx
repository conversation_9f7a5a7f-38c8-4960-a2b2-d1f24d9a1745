import ShapeBlur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black flex flex-col items-center justify-center">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full">
        {/* Full Width Logo with ShapeBlur Effect Container */}
        <div className="relative flex items-center justify-center mb-16">
          {/* ShapeBlur Effect - Full screen container */}
          <div className="w-[100vw] h-[40vh] relative">
            <ShapeBlur
              variation={0}
              shapeSize={1.0}
              roundness={0.05}
              borderSize={0.25}
              circleSize={0.7}
              circleEdge={0.2}
            />
            {/* Full Width Logo positioned absolutely in center */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 bg-transparent">
              <img
                src="/logo.png"
                alt="Logo"
                className="w-[95vw] h-[20vh] object-contain bg-transparent"
                style={{
                  filter: 'drop-shadow(0 0 60px rgba(255,255,255,1)) brightness(3) contrast(3) saturate(2) hue-rotate(0deg)',
                  mixBlendMode: 'screen',
                  background: 'transparent !important',
                  backgroundColor: 'transparent !important',
                  backdropFilter: 'none',
                  isolation: 'isolate'
                }}
              />
            </div>
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-thin text-white tracking-widest">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App

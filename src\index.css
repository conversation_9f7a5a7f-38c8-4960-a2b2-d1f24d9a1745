:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #000000;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
}

#root {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.maintenance-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.shapeblur-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.content-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo-blur-container {
  position: relative;
  width: 300px;
  height: 300px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shapeblur-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.logo-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  max-width: 120px;
  max-height: 120px;
  width: auto;
  height: auto;
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
  transition: filter 300ms ease;
}

.logo:hover {
  filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.7));
}

.maintenance-text h1 {
  font-size: 3rem;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  letter-spacing: 0.1em;
  margin: 0;
}

@media (max-width: 768px) {
  .logo-blur-container {
    width: 250px;
    height: 250px;
  }

  .logo {
    max-width: 100px;
    max-height: 100px;
  }

  .maintenance-text h1 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .logo-blur-container {
    width: 200px;
    height: 200px;
  }

  .logo {
    max-width: 80px;
    max-height: 80px;
  }

  .maintenance-text h1 {
    font-size: 1.5rem;
  }
}
